import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Trash2, MessageSquare } from "lucide-react";
import { ChatSession, deleteChatSession } from "@/utils/chatHistory";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useTheme } from "@/hooks/use-theme";
import { ThemeToggle } from "@/components/ThemeToggle";
import { formatDistanceToNow } from "date-fns";
import { id } from "date-fns/locale";

interface ChatSidebarProps {
  sessions: ChatSession[];
  currentSessionId: string | null;
  onSelectSession: (sessionId: string) => void;
  onNewChat: () => void;
}

export function ChatSidebar({ 
  sessions, 
  currentSessionId, 
  onSelectSession, 
  onNewChat 
}: ChatSidebarProps) {
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const { theme } = useTheme();

  const handleDeleteClick = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation();
    setConfirmDelete(sessionId);
  };

  const handleConfirmDelete = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation();
    deleteChatSession(sessionId);
    setConfirmDelete(null);
  };

  const handleCancelDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setConfirmDelete(null);
  };

  return (
    <div className="w-64 h-screen flex flex-col border-r dark:border-gray-700">
      <div className="p-4 border-b dark:border-gray-700 flex justify-between items-center">
        <h2 className="font-bold text-lg">KIKAZE-AI</h2>
        <ThemeToggle />
      </div>
      
      <div className="p-4">
        <Button 
          onClick={onNewChat} 
          className="w-full flex items-center justify-center gap-2"
        >
          <PlusCircle size={16} />
          <span>Chat Baru</span>
        </Button>
      </div>
      
      <ScrollArea className="flex-1 px-4">
        <div className="space-y-2">
          {sessions.map((session) => (
            <div
              key={session.id}
              onClick={() => onSelectSession(session.id)}
              className={`p-2 rounded-lg cursor-pointer flex items-center justify-between group ${
                currentSessionId === session.id
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-secondary"
              }`}
            >
              <div className="flex items-center gap-2 overflow-hidden">
                <MessageSquare size={16} />
                <div className="truncate">
                  <div className="font-medium truncate">{session.title}</div>
                  <div className="text-xs opacity-70 truncate">
                    {formatDistanceToNow(new Date(session.updatedAt), { 
                      addSuffix: true,
                      locale: id 
                    })}
                  </div>
                </div>
              </div>
              
              {confirmDelete === session.id ? (
                <div className="flex items-center gap-1">
                  <Button
                    variant="destructive"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => handleConfirmDelete(e, session.id)}
                  >
                    <Trash2 size={12} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={handleCancelDelete}
                  >
                    ✕
                  </Button>
                </div>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 opacity-0 group-hover:opacity-100"
                  onClick={(e) => handleDeleteClick(e, session.id)}
                >
                  <Trash2 size={12} />
                </Button>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
      
      <div className="p-4 border-t dark:border-gray-700">
        <div className="text-xs text-center text-muted-foreground">
          © 2023 KIKAZE-AI
        </div>
      </div>
    </div>
  );
}