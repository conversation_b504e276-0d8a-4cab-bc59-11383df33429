import { Message } from "@/types/chat";

// Tipe untuk menyimpan riwayat chat
export type ChatSession = {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
};

const STORAGE_KEY = "chat_sessions";

// Fungsi untuk mendapatkan semua sesi chat
export const getAllChatSessions = (): ChatSession[] => {
  const sessions = localStorage.getItem(STORAGE_KEY);
  return sessions ? JSON.parse(sessions) : [];
};

// Fungsi untuk mendapatkan sesi chat berdasarkan ID
export const getChatSessionById = (id: string): ChatSession | undefined => {
  const sessions = getAllChatSessions();
  return sessions.find(session => session.id === id);
};

// Fungsi untuk membuat sesi chat baru
export const createChatSession = (title: string = "New Chat"): ChatSession => {
  const sessions = getAllChatSessions();
  const newSession: ChatSession = {
    id: Date.now().toString(),
    title,
    messages: [],
    createdAt: Date.now(),
    updatedAt: Date.now()
  };
  
  localStorage.setItem(STORAGE_KEY, JSON.stringify([newSession, ...sessions]));
  return newSession;
};

// Fungsi untuk memperbarui sesi chat
export const updateChatSession = (session: ChatSession): void => {
  const sessions = getAllChatSessions();
  const index = sessions.findIndex(s => s.id === session.id);
  
  if (index !== -1) {
    sessions[index] = {
      ...session,
      updatedAt: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(sessions));
  }
};

// Fungsi untuk menghapus sesi chat
export const deleteChatSession = (id: string): void => {
  const sessions = getAllChatSessions();
  const filteredSessions = sessions.filter(session => session.id !== id);
  localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredSessions));
};

// Fungsi untuk menambahkan pesan ke sesi chat
export const addMessageToChatSession = (sessionId: string, message: Message): void => {
  const session = getChatSessionById(sessionId);
  
  if (session) {
    session.messages.push(message);
    updateChatSession(session);
  }
};

// Fungsi untuk menghasilkan judul otomatis dari pesan pertama
export const generateTitleFromMessage = (message: string): string => {
  // Potong pesan jika terlalu panjang
  const maxLength = 30;
  if (message.length <= maxLength) return message;
  return message.substring(0, maxLength) + "...";
};