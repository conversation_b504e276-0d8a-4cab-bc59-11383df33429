
// utils/apiKeys.ts

export const API_KEYS = {
  GROQ: "VITE_GROQ_API_KEY",
  HUGGINGFACE: "VITE_HUGGINGFACE_API_KEY",
} as const;

// Tambahkan fungsi untuk enkripsi/dekripsi
const encryptApiKey = (key: string): string => {
  // Enkripsi sederhana, sebaiknya gunakan library enkripsi yang lebih kuat
  return btoa(key);
};

const decryptApiKey = (encryptedKey: string): string => {
  // Dekripsi sederhana
  return atob(encryptedKey);
};

export const getApiKey = (keyName: string): string | undefined => {
  switch (keyName) {
    case API_KEYS.GROQ:
      return import.meta.env.VITE_GROQ_API_KEY || 
        (localStorage.getItem(API_KEYS.GROQ) ? decryptApiKey(localStorage.getItem(API_KEYS.GROQ)!) : undefined);
    case API_KEYS.HUGGINGFACE:
      return import.meta.env.VITE_HUGGINGFACE_API_KEY || 
        (localStorage.getItem(API_KEYS.HUGGINGFACE) ? decryptApiKey(localStorage.getItem(API_KEYS.HUGGINGFACE)!) : undefined);
    default:
      return undefined;
  }
};

export const saveApiKey = (key: string, value: string) => {
  localStorage.setItem(key, encryptApiKey(value));
};

export const loadApiKeyFromLocalStorage = (key: string): string | null => {
  return localStorage.getItem(key);
};
