import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Download, Upload } from "lucide-react";
import { getAllChatSessions, ChatSession } from "@/utils/chatHistory";
import { toast } from "sonner";

interface ExportImportChatProps {
  onImport: () => void;
}

export function ExportImportChat({ onImport }: ExportImportChatProps) {
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);

  const handleExport = () => {
    try {
      const sessions = getAllChatSessions();
      const dataStr = JSON.stringify(sessions, null, 2);
      const dataUri = "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);
      
      const exportFileDefaultName = `kikaze-ai-chats-${new Date().toISOString().slice(0, 10)}.json`;
      
      const linkElement = document.createElement("a");
      linkElement.setAttribute("href", dataUri);
      linkElement.setAttribute("download", exportFileDefaultName);
      linkElement.click();
      
      toast.success("Chat berhasil diekspor!");
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor chat");
    }
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const importedSessions = JSON.parse(content) as ChatSession[];
        
        // Validate imported data
        if (!Array.isArray(importedSessions)) {
          throw new Error("Format data tidak valid");
        }
        
        // Store imported sessions
        localStorage.setItem("chat_sessions", JSON.stringify(importedSessions));
        
        // Close dialog and notify parent
        setIsImportDialogOpen(false);
        onImport();
        
        toast.success("Chat berhasil diimpor!");
      } catch (error) {
        console.error("Import error:", error);
        toast.error("Gagal mengimpor chat: Format file tidak valid");
      }
    };
    
    reader.readAsText(file);
  };

  return (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" onClick={handleExport}>
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>
      
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Chat</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Pilih file JSON yang berisi data chat yang ingin diimpor. 
              Ini akan menggantikan semua chat yang ada saat ini.
            </p>
            <div className="flex justify-center">
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-semibold
                  file:bg-primary file:text-primary-foreground
                  hover:file:bg-primary/90"
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}