
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { getApiKey, API_KEYS } from "@/utils/apiKeys";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Copy, Maximize2, Download, Edit, Image, Save } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import "highlight.js/styles/github-dark.css"; // Tema highlight


interface Message {
  role: "user" | "assistant";
  content: string;
  type?: "text" | "code" | "image";
  imageUrl?: string;
  language?: string;
}

const LOCAL_STORAGE_KEY = "chat_messages";

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

const Chat = () => {
  const navigate = useNavigate();
  const [messages, setMessages] = useState<Message[]>([]);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<SpeechSynthesisVoice | null>(null);
  const [recognition, setRecognition] = useState<(typeof window.SpeechRecognition | typeof window.webkitSpeechRecognition) | null>(null);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [editingCode, setEditingCode] = useState<string | null>(null);
  const [previewCode, setPreviewCode] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const renderMessageContent = (content: string) => (
    <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeHighlight]}>
      {content}
    </ReactMarkdown>
  );
  const speakText = (text: string) => {
    if ("speechSynthesis" in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = "id-ID";
      utterance.rate = 1.0;
      utterance.pitch = 1.0;
      speechSynthesis.speak(utterance);
    } else {
      alert("Browser tidak mendukung fitur Text-to-Speech!");
    }
  };

  const stopSpeaking = () => {
    if ("speechSynthesis" in window) {
      speechSynthesis.cancel();
    }
  };

  const startListening = () => {
    if ("webkitSpeechRecognition" in window || "SpeechRecognition" in window) {
      const recognition = new ((window as any).SpeechRecognition || (window as any).webkitSpeechRecognition)();
      recognition.lang = "id-ID";
      recognition.continuous = false;
      recognition.interimResults = false;

      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInput(transcript);
        handleSubmit(event);
      };

      recognition.start();
    } else {
      alert("Browser tidak mendukung fitur Speech-to-Text!");
    }
  };


  // Load messages from localStorage on initial render
  useEffect(() => {
    const savedMessages = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedMessages) {
      try {
        setMessages(JSON.parse(savedMessages));
      } catch (error) {
        console.error("Error parsing saved messages:", error);
      }
    }
  }, []);

// Add this new function to delete history
const hapusHistory = () => {
  setMessages([]);
  localStorage.removeItem(LOCAL_STORAGE_KEY);
  setShowDeleteConfirm(false);
  toast.success("Riwayat chat telah dihapus");
};

  // Save messages to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(messages));
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage: Message = { role: "user", content: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    const groqKey = getApiKey(API_KEYS.GROQ);
    if (!groqKey) {
      toast.error("API key tidak ditemukan. Silakan atur API key terlebih dahulu");
      setIsLoading(false);
      navigate("/");
      return;
    }

    try {
      const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${groqKey}`,
        },
        body: JSON.stringify({
          model: "deepseek-r1-distill-qwen-32b",
          messages: [
            ...messages.map(({ role, content }) => ({ role, content })),
            { role: userMessage.role, content: userMessage.content }
          ],
          temperature: 0.7,
          max_tokens: 2048,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to get response from Groq API");
      }

      const data = await response.json();
      const content = data.choices[0].message.content;
      
      const codeBlockRegex = /```([a-zA-Z]*)\n([\s\S]*?)```/g;
      let match;
      let lastIndex = 0;
      const processedMessages: Message[] = [];

      while ((match = codeBlockRegex.exec(content)) !== null) {
        if (match.index > lastIndex) {
          const textContent = content.slice(lastIndex, match.index).trim();
          if (textContent) {
            processedMessages.push({
              role: "assistant",
              content: textContent,
              type: "text"
            });
          }
        }

        processedMessages.push({
          role: "assistant",
          content: match[2].trim(),
          type: "code",
          language: match[1] || "plaintext"
        });

        lastIndex = match.index + match[0].length;
      }

      if (lastIndex < content.length) {
        const remainingText = content.slice(lastIndex).trim();
        if (remainingText) {
          processedMessages.push({
            role: "assistant",
            content: remainingText,
            type: "text"
          });
        }
      }

      setMessages((prev) => [...prev, ...processedMessages]);
    } catch (error) {
      console.error("Error:", error);
      toast.error("Gagal mengirim pesan ke Groq API");
    } finally {
      setIsLoading(false);
    }
  };

  const generateImage = async (prompt: string) => {
    const hfToken = getApiKey(API_KEYS.HUGGINGFACE);
    if (!hfToken) {
      toast.error("API token tidak ditemukan. Silakan atur Hugging Face token terlebih dahulu");
      navigate("/");
      return;
    }

    setIsGeneratingImage(true);
    try {
      const response = await fetch(
        "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-3.5-large",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${hfToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            inputs: prompt,
            parameters: {
              width: 1024,
              height: 576,
              negative_prompt: "lowres, bad anatomy, bad hands, cropped, worst quality",
              num_inference_steps: 30,
              guidance_scale: 7.5,
            },
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const imageUrl = URL.createObjectURL(blob);

      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "Here's your generated image:",
          type: "image",
          imageUrl,
        },
      ]);
      toast.success("Gambar berhasil dibuat");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Gagal menghasilkan gambar. Mohon coba lagi.");
    } finally {
      setIsGeneratingImage(false);
    }
  };

  const handleImageGeneration = () => {
    if (!input.trim()) {
      toast.error("Mohon masukkan deskripsi gambar");
      return;
    }
    generateImage(input);
    setInput("");
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Kode berhasil disalin");
    } catch (err) {
      toast.error("Gagal menyalin kode");
    }
  };

  const downloadImage = (imageUrl: string) => {
    const a = document.createElement("a");
    a.href = imageUrl;
    a.download = "generated-image.png";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleSaveCode = () => {
    if (editingCode !== null) {
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.type === "code" && msg.content === previewCode
            ? { ...msg, content: editingCode }
            : msg
        )
      );
      setEditingCode(null);
      setPreviewCode(editingCode);
      toast.success("Kode berhasil disimpan");
    }
  };

  return (
    
    <div className="bg-gray-100 max-h-screen flex items-center justify-center">
      <div className="bg-white w-full max-w-8xl">
        <Card className="h-[90vh] flex flex-col">
          {/* Add delete button at the top */}
          <div className="p-4 border-b bg-gray-100 flex justify-between items-center">
            <h1 className="text-2xl font-bold">KIKAZE-AI</h1>
            <Button
            className="bg-red-500"
              variant="outline"
              color="danger"
              onClick={() => setShowDeleteConfirm(true)}
            >
              Hapus History
            </Button>
          </div>
          {/* ... existing messages display ... */}

          {/* Add confirmation dialog */}
          <Dialog
            open={showDeleteConfirm}
            onOpenChange={setShowDeleteConfirm}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Konfirmasi Hapus</DialogTitle>
                <DialogDescription>
                  Apakah Anda yakin ingin menghapus seluruh riwayat chat?
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Batal
                </Button>
                <Button color="danger" onClick={hapusHistory}>
                  Hapus
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
  {messages.map((message, index) => (
    <div
      key={index}
      className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
    >
      <div
        className={`max-w-[80%] rounded-lg p-3 ${
          message.role === "user"
            ? "bg-blue-500 text-white"
            : message.type === "code"
            ? "bg-gray-900 text-white font-mono"
            : "bg-gray-200 text-gray-800"
        }`}
      >
        {message.type === "code" ? (
          <div className="relative">
            <pre className="overflow-x-auto p-2">
              <code>{message.content}</code>
            </pre>
            <div className="absolute top-2 right-2 space-x-2">
              <Button variant="ghost"size="sm"onClick={() => {setPreviewCode(message.content);setShowPreview(true); }}
                        ><Maximize2 className="w-4 h-4" />
                        </Button>
              <Button variant="ghost" size="sm" onClick={() => copyToClipboard(message.content)}
                        > <Copy className="w-4 h-4" />
                        </Button>
              <Button  variant="ghost"  size="sm"  onClick={() => {setEditingCode(message.content);setPreviewCode(message.content);}}
                        ><Edit className="w-4 h-4" />
                        </Button>
              </div>
          </div>
        ) : message.type === "image" ? (
          <div className="space-y-2">
            {message.content}
            {message.imageUrl && (
              <div className="relative group">
                <img
                  src={message.imageUrl}
                  alt="Generated"
                  className="rounded-lg max-w-full cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => setSelectedImageUrl(message.imageUrl)}
                />
                <div className="absolute top-2 right-2 space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button variant="secondary" size="sm" onClick={() => downloadImage(message.imageUrl)}>
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <ReactMarkdown>{message.content}</ReactMarkdown>
        )}
      </div>
    </div>
  ))}
</div>
          <form onSubmit={handleSubmit} className="p-4 border-t">
            <div className="flex gap-2">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ketik pesan Anda..."
                disabled={isLoading || isGeneratingImage}
              />
              <Button
                type="submit"
                disabled={isLoading || isGeneratingImage}
                onClick={(e) => {
                  e.preventDefault();
                  handleSubmit(e);
                }}
              >
                {isLoading ? "Memproses..." : "Kirim"}
              </Button>
              <Button
                type="button"
                variant="secondary"
                disabled={isLoading || isGeneratingImage}
                onClick={handleImageGeneration}
              >
                <Image className="w-4 h-4 mr-2" />
                {isGeneratingImage ? "Membuat..." : "Buat Gambar"}
              </Button>
              </div>
              <div className="p-4">
      <div className="flex gap-2 mb-4">
      <Button onClick={() => speakText(messages.filter(m => m.role === "assistant").slice(-1)[0]?.content || "")}>🔊 </Button>
      <Button onClick={stopSpeaking}>⏹</Button>
      <Button onClick={startListening}>🎤</Button>
      </div>
      
    </div>
            
          </form>
        </Card>

        {/* Code Preview Dialog */}
        <Dialog open={showPreview} onOpenChange={setShowPreview}>
          <DialogContent className="max-w-[90vw] max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Preview Kode</DialogTitle>
              <DialogDescription>
                Preview dari kode yang dipilih
              </DialogDescription>
            </DialogHeader>
            <div className="bg-white rounded-lg overflow-hidden">
              {previewCode && (
                <iframe
                  srcDoc={`
                    <!DOCTYPE html>
                    <html>
                      <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <script src="https://cdn.tailwindcss.com"></script>
                      </head>
                      <body>
                        ${previewCode}
                      </body>
                    </html>
                  `}
                  className="w-full h-[60vh] bg-white rounded-lg"
                  title="Code Preview"
                />
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Image Preview Dialog */}
        <Dialog open={!!selectedImageUrl} onOpenChange={() => setSelectedImageUrl(null)}>
          <DialogContent className="max-w-[90vw] max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Preview Gambar</DialogTitle>
              <DialogDescription>
                Preview dari gambar yang dipilih
              </DialogDescription>
            </DialogHeader>
            {selectedImageUrl && (
              <img
                src={selectedImageUrl}
                alt="Preview"
                className="max-w-full max-h-[80vh] rounded-lg"
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Code Editor Dialog */}
        <Dialog open={!!editingCode} onOpenChange={() => setEditingCode(null)}>
          <DialogContent className="max-w-[90vw] max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Edit Kode</DialogTitle>
              <DialogDescription>
                Edit kode yang dipilih
              </DialogDescription>
            </DialogHeader>
            <div className="max-w-[90vw] max-h-[90vh]">
              <textarea
                value={editingCode || ""}
                onChange={(e) => setEditingCode(e.target.value)}
                className="w-full h-[400px] font-mono p-4 bg-gray-800 text-white rounded-lg"
              />
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingCode(null)}>
                  Batal
                </Button>
                <Button onClick={handleSaveCode}>
                  <Save className="w-4 h-4 mr-2" />
                  Simpan
                </Button>
                
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>

    
  );
};

export default Chat;
