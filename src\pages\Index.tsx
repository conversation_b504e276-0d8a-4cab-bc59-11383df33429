import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { saveApiKey, getApiKey, API_KEYS } from "@/utils/apiKeys";

const Index = () => {
  const navigate = useNavigate();
  const [groqKey, setGroqKey] = useState("");
  const [huggingfaceToken, setHuggingfaceToken] = useState("");
  const [errors, setErrors] = useState({ groq: "", huggingface: "" });

  // Check if API keys are already saved
  useEffect(() => {
    const savedGroqKey = getApiKey(API_KEYS.GROQ);
    const savedHuggingfaceToken = getApiKey(API_KEYS.HUGGINGFACE);

    if (savedGroqKey && savedHuggingfaceToken) {
      navigate("/chat"); // Redirect to chat if keys exist
    } else {
      setGroqKey(savedGroqKey || "");
      setHuggingfaceToken(savedHuggingfaceToken || "");
    }
  }, [navigate]);

  const validateInputs = () => {
    const newErrors = { groq: "", huggingface: "" };
    let isValid = true;

    if (!groqKey.trim()) {
      newErrors.groq = "Groq API Key tidak boleh kosong";
      isValid = false;
    } else if (!groqKey.startsWith("gsk_")) {
      newErrors.groq = "Format Groq API Key tidak valid";
      isValid = false;
    }

    if (!huggingfaceToken.trim()) {
      newErrors.huggingface = "Hugging Face API Token tidak boleh kosong";
      isValid = false;
    } else if (!huggingfaceToken.startsWith("hf_")) {
      newErrors.huggingface = "Format Hugging Face API Token tidak valid";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSaveKeys = () => {
    if (validateInputs()) {
      saveApiKey(API_KEYS.GROQ, groqKey);
      saveApiKey(API_KEYS.HUGGINGFACE, huggingfaceToken);
      toast.success("API keys berhasil disimpan!");
      navigate("/chat");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="max-w-md w-full">
        <Card className="p-6 space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="groq">Groq API Key</Label>
              <Input
                id="groq"
                type="password"
                value={groqKey}
                onChange={(e) => setGroqKey(e.target.value)}
                placeholder="Enter your Groq API key"
                className={errors.groq ? "border-red-500" : ""}
              />
              {errors.groq && <p className="text-red-500 text-sm">{errors.groq}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="huggingface">Hugging Face API Token</Label>
              <Input
                id="huggingface"
                type="password"
                value={huggingfaceToken}
                onChange={(e) => setHuggingfaceToken(e.target.value)}
                placeholder="Enter your Hugging Face API token"
                className={errors.huggingface ? "border-red-500" : ""}
              />
              {errors.huggingface && <p className="text-red-500 text-sm">{errors.huggingface}</p>}
            </div>
          </div>
          <Button className="w-full" onClick={handleSaveKeys}>
            Save API Keys
          </Button>
        </Card>
        <div className="text-center text-sm text-gray-500 mt-4">
          <p>API keys Anda disimpan dengan aman di penyimpanan lokal browser Anda.</p>
        </div>
      </div>
    </div>
  );
};

export default Index;
